#!/usr/bin/env python3
"""
Daily Automation Script for Clipper_Neon
========================================

Complete automation workflow:
1. Process YouTube videos with perfect timestamps
2. Generate viral clips with AI
3. Auto-upload to social media platforms
4. Track performance and optimize

Run this daily for hands-off content creation.
"""

import sys
import json
import time
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from config import ClipperConfig
from clipper_main import Clipper<PERSON>eon
from auto_uploader import AutoUploader, UploadJob
from auto_processor import AutoProcessor


class DailyAutomation:
    """Handles complete daily automation workflow."""
    
    def __init__(self):
        """Initialize the automation system."""
        self.config = ClipperConfig()
        self.clipper = ClipperNeon()
        self.uploader = AutoUploader(self.config)
        self.processor = AutoProcessor(self.config)
        
        print("🚀 Clipper_Neon Daily Automation")
        print("=" * 50)
    
    def run_daily_workflow(self, urls: List[str], platforms: List[str] = None):
        """
        Run the complete daily automation workflow.
        
        Args:
            urls: List of YouTube URLs to process
            platforms: List of platforms to upload to
        """
        if platforms is None:
            platforms = ['youtube_shorts', 'tiktok']
        
        print(f"📅 Starting daily workflow: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📹 Processing {len(urls)} videos")
        print(f"📱 Target platforms: {', '.join(platforms)}")
        print()
        
        all_clips = []
        all_upload_jobs = []
        
        for i, url in enumerate(urls, 1):
            print(f"🎬 Processing video {i}/{len(urls)}: {url}")
            
            try:
                # Process video with enhanced transcript
                clips = self.clipper.process_video(url)
                
                if clips:
                    print(f"  ✅ Generated {len(clips)} clips")
                    all_clips.extend(clips)
                    
                    # Create upload jobs for each platform
                    for clip_path in clips:
                        for platform in platforms:
                            job = self.create_upload_job(clip_path, platform, url)
                            all_upload_jobs.append(job)
                else:
                    print(f"  ⚠️  No clips generated")
                
            except Exception as e:
                print(f"  ❌ Processing failed: {e}")
                continue
            
            print()
        
        # Upload all clips
        if all_upload_jobs:
            print(f"📤 Uploading {len(all_upload_jobs)} clips to platforms...")
            self.batch_upload(all_upload_jobs)
        
        # Generate daily report
        self.generate_daily_report(all_clips, all_upload_jobs)
        
        print("🎉 Daily automation workflow completed!")
    
    def create_upload_job(self, clip_path: str, platform: str, source_url: str) -> UploadJob:
        """Create an upload job for a clip."""
        clip_name = Path(clip_path).stem
        
        # Extract title from filename or use default
        if "_" in clip_name:
            title_part = clip_name.split("_", 2)[-1].replace("_", " ")
            title = title_part[:50]  # Platform title limits
        else:
            title = "Viral Moment"
        
        # Platform-specific descriptions
        descriptions = {
            'youtube_shorts': f"{title} #Shorts #Viral #Trending",
            'tiktok': f"{title} #fyp #viral #trending #shorts",
            'instagram': f"{title} #reels #viral #trending #explore"
        }
        
        return UploadJob(
            platform=platform,
            video_path=clip_path,
            title=title,
            description=descriptions.get(platform, title),
            tags=['viral', 'trending', 'shorts']
        )
    
    def batch_upload(self, upload_jobs: List[UploadJob]):
        """Upload clips in batches to avoid rate limiting."""
        # Group by platform
        platform_jobs = {}
        for job in upload_jobs:
            if job.platform not in platform_jobs:
                platform_jobs[job.platform] = []
            platform_jobs[job.platform].append(job)
        
        # Upload to each platform
        for platform, jobs in platform_jobs.items():
            print(f"  📱 Uploading {len(jobs)} clips to {platform}")
            
            try:
                results = self.uploader.upload_batch(jobs)
                successful = sum(1 for success in results.values() if success)
                print(f"    ✅ {successful}/{len(jobs)} uploads successful")
                
                # Wait between platforms to avoid rate limiting
                if len(platform_jobs) > 1:
                    print("    ⏳ Waiting 30s before next platform...")
                    time.sleep(30)
                
            except Exception as e:
                print(f"    ❌ Upload failed for {platform}: {e}")
    
    def generate_daily_report(self, clips: List[str], upload_jobs: List[UploadJob]):
        """Generate a daily automation report."""
        report = {
            'date': datetime.now().isoformat(),
            'summary': {
                'total_clips_generated': len(clips),
                'total_upload_jobs': len(upload_jobs),
                'platforms': list(set(job.platform for job in upload_jobs)),
                'clips_per_platform': {}
            },
            'clips': [
                {
                    'path': clip,
                    'filename': Path(clip).name,
                    'size_mb': round(Path(clip).stat().st_size / 1024 / 1024, 2) if Path(clip).exists() else 0
                }
                for clip in clips
            ],
            'upload_jobs': [
                {
                    'platform': job.platform,
                    'title': job.title,
                    'video_file': Path(job.video_path).name
                }
                for job in upload_jobs
            ]
        }
        
        # Count clips per platform
        for job in upload_jobs:
            platform = job.platform
            if platform not in report['summary']['clips_per_platform']:
                report['summary']['clips_per_platform'][platform] = 0
            report['summary']['clips_per_platform'][platform] += 1
        
        # Save report
        reports_dir = Path(self.config.output_dir) / "daily_reports"
        reports_dir.mkdir(exist_ok=True)
        
        report_file = reports_dir / f"daily_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"📊 Daily report saved: {report_file}")
        
        # Print summary
        print("\n📈 Daily Summary:")
        print(f"  🎬 Clips generated: {len(clips)}")
        print(f"  📤 Upload jobs created: {len(upload_jobs)}")
        for platform, count in report['summary']['clips_per_platform'].items():
            print(f"    {platform}: {count} clips")


def main():
    """Main entry point for daily automation."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Clipper_Neon Daily Automation")
    
    parser.add_argument(
        "urls",
        nargs="+",
        help="YouTube URLs to process (or path to file containing URLs)"
    )
    
    parser.add_argument(
        "--platforms",
        nargs="+",
        default=['youtube_shorts', 'tiktok'],
        help="Platforms to upload to"
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Process videos but don't upload"
    )
    
    args = parser.parse_args()
    
    # Handle URL file input
    urls = []
    for url_input in args.urls:
        if Path(url_input).exists():
            # Read URLs from file
            with open(url_input, 'r') as f:
                file_urls = [line.strip() for line in f if line.strip()]
            urls.extend(file_urls)
        else:
            # Direct URL
            urls.append(url_input)
    
    if not urls:
        print("❌ No URLs provided")
        return 1
    
    # Initialize automation
    automation = DailyAutomation()
    
    if args.dry_run:
        print("🧪 DRY RUN MODE - No uploads will be performed")
        # Process videos only
        for url in urls:
            try:
                clips = automation.clipper.process_video(url)
                print(f"✅ {url}: {len(clips)} clips generated")
            except Exception as e:
                print(f"❌ {url}: {e}")
    else:
        # Run full automation
        automation.run_daily_workflow(urls, args.platforms)
    
    return 0


if __name__ == "__main__":
    sys.exit(main())


# Example usage:
"""
# Process single video
python daily_automation.py "https://youtube.com/watch?v=VIDEO_ID"

# Process multiple videos
python daily_automation.py "https://youtube.com/watch?v=ID1" "https://youtube.com/watch?v=ID2"

# Process from file
python daily_automation.py urls.txt

# Custom platforms
python daily_automation.py "URL" --platforms youtube_shorts tiktok instagram

# Dry run (no uploads)
python daily_automation.py "URL" --dry-run
"""
