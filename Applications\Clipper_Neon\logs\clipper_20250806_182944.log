2025-08-06 18:29:44,553 - transcription_engine - INFO - YouTube Transcript API available for perfect timestamps
2025-08-06 18:29:47,427 - transcription_engine - INFO - Whisper is available for transcription fallback
2025-08-06 18:29:49,457 - highlight_detector - INFO - Connected to Ollama. Using model: deepseek-r1:latest
2025-08-06 18:29:49,458 - __main__ - INFO - Starting processing for: https://www.youtube.com/watch?v=b-zrndQbsZw
2025-08-06 18:29:49,459 - __main__ - INFO - Step 1: Downloading video...
2025-08-06 18:29:49,459 - video_downloader - INFO - Starting download for: https://www.youtube.com/watch?v=b-zrndQbsZw
2025-08-06 18:29:49,459 - video_downloader - INFO - Fetching video info for: https://www.youtube.com/watch?v=b-zrndQbsZw
2025-08-06 18:29:53,327 - video_downloader - INFO - Video info retrieved: N<PERSON><PERSON> Reunites with <PERSON><PERSON>! (700s)
2025-08-06 18:29:53,328 - video_downloader - INFO - Executing yt-dlp download...
2025-08-06 18:29:57,408 - video_downloader - INFO - Download completed: C:\AI-Hub\Applications\Clipper_Neon\temp\b-zrndQbsZw.mp4
2025-08-06 18:29:57,408 - __main__ - INFO - Step 2: Generating transcript...
2025-08-06 18:29:57,409 - transcription_engine - INFO - Starting transcription for: C:\AI-Hub\Applications\Clipper_Neon\temp\b-zrndQbsZw.mp4
2025-08-06 18:29:57,409 - transcription_engine - INFO - Fetching YouTube transcript for video ID: b-zrndQbsZw
2025-08-06 18:29:57,409 - transcription_engine - WARNING - YouTube transcript extraction failed: type object 'YouTubeTranscriptApi' has no attribute 'get_transcript'
2025-08-06 18:29:57,409 - transcription_engine - INFO - Extracting audio from video...
2025-08-06 18:29:57,738 - transcription_engine - INFO - Audio extracted: C:\Users\<USER>\AppData\Local\Temp\tmp4el4wy0j.wav (21.4 MB)
2025-08-06 18:29:57,738 - transcription_engine - INFO - Loading Whisper model...
2025-08-06 18:29:58,710 - transcription_engine - ERROR - Whisper transcription failed: Numpy is not available
2025-08-06 18:29:58,711 - transcription_engine - WARNING - Using fallback transcription method
2025-08-06 18:29:58,711 - transcription_engine - WARNING - speech_recognition library not available
2025-08-06 18:29:58,711 - transcription_engine - WARNING - No transcription method available. Using placeholder.
2025-08-06 18:29:58,712 - transcription_engine - INFO - Transcription completed. Length: 76 characters
2025-08-06 18:29:58,714 - __main__ - INFO - Step 3: Detecting highlights with AI...
2025-08-06 18:29:58,714 - highlight_detector - INFO - Detecting highlights using deepseek-r1:latest
2025-08-06 18:30:19,962 - highlight_detector - INFO - Parsed 2 highlights from AI response
2025-08-06 18:30:19,962 - highlight_detector - INFO - Raw highlights before filtering: 2
2025-08-06 18:30:19,962 - highlight_detector - INFO - Skipping long highlight: 'Trippie Redd's Hidden Talent Revealed!' duration=333.0s (max=60.0s)
2025-08-06 18:30:19,981 - highlight_detector - INFO - Detected 1 valid highlights
2025-08-06 18:30:19,981 - __main__ - INFO - Step 4: Extracting video clips...
2025-08-06 18:30:19,981 - video_clipper - INFO - Extracting clip 1: The Moment N3on Admits His Biggest Mistake (587.0s-643.0s)
2025-08-06 18:30:23,311 - video_clipper - INFO - Clip extracted successfully: C:\AI-Hub\Applications\Clipper_Neon\data_out\clip_01_The_Moment_N3on_Admits_His_Biggest_Mistake.mp4 (5.3 MB)
2025-08-06 18:30:23,311 - __main__ - INFO - Step 5: Post-processing clips...
2025-08-06 18:30:23,311 - post_processor - INFO - Post-processing 1 clips
2025-08-06 18:30:23,312 - post_processor - INFO - Created output structure: C:\AI-Hub\Applications\Clipper_Neon\data_out\20250806_183023_N3on_Reunites_with_Trippie_Redd!
2025-08-06 18:30:27,802 - post_processor - INFO - Processed clip: C:\AI-Hub\Applications\Clipper_Neon\data_out\20250806_183023_N3on_Reunites_with_Trippie_Redd!\clips\clip_01__think__Okay,_let's_tackle_this._The_user_wants_a.mp4
2025-08-06 18:30:27,802 - post_processor - INFO - Summary report saved: C:\AI-Hub\Applications\Clipper_Neon\data_out\20250806_183023_N3on_Reunites_with_Trippie_Redd!\processing_report.json
2025-08-06 18:30:27,802 - post_processor - INFO - Post-processing completed. 1 clips ready
2025-08-06 18:30:27,803 - __main__ - INFO - Successfully generated 1 clips
