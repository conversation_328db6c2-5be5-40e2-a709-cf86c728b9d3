"""
Transcription Engine for Clipper_Neon
=====================================

Handles audio extraction and transcription using Whisper or similar ASR.
"""

import os
import subprocess
import logging
import tempfile
from pathlib import Path
from typing import Optional, Dict, Any


class TranscriptionEngine:
    """Handles audio extraction and transcription."""
    
    def __init__(self, config):
        """Initialize the transcription engine."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Check if Whisper is available
        self._check_whisper_availability()
    
    def _check_whisper_availability(self):
        """Check if Whisper is available for transcription."""
        try:
            import whisper
            self.whisper_available = True
            self.logger.info("Whisper is available for transcription")
        except ImportError:
            self.whisper_available = False
            self.logger.warning("Whisper not available. Install with: pip install openai-whisper")
    
    def transcribe(self, video_path: str) -> str:
        """
        Transcribe video to text.
        
        Args:
            video_path: Path to video file
            
        Returns:
            Transcript text
        """
        self.logger.info(f"Starting transcription for: {video_path}")
        
        # Extract audio from video
        audio_path = self._extract_audio(video_path)
        
        try:
            # Transcribe audio to text
            if self.whisper_available:
                transcript = self._transcribe_with_whisper(audio_path)
            else:
                transcript = self._transcribe_fallback(audio_path)
            
            self.logger.info(f"Transcription completed. Length: {len(transcript)} characters")
            return transcript
            
        finally:
            # Clean up temporary audio file
            if Path(audio_path).exists():
                Path(audio_path).unlink()
                self.logger.debug(f"Cleaned up audio file: {audio_path}")
    
    def _extract_audio(self, video_path: str) -> str:
        """Extract audio from video using ffmpeg."""
        self.logger.info("Extracting audio from video...")
        
        # Create temporary audio file
        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_audio:
            audio_path = temp_audio.name
        
        # Build ffmpeg command for audio extraction
        cmd = [
            self.config.ffmpeg_path,
            "-i", video_path,
            "-vn",  # No video
            "-acodec", "pcm_s16le",  # PCM 16-bit little-endian
            "-ar", "16000",  # 16kHz sample rate (good for speech)
            "-ac", "1",  # Mono
            "-y",  # Overwrite output
            audio_path
        ]
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                check=True
            )
            
            if not Path(audio_path).exists():
                raise FileNotFoundError("Audio extraction failed - no output file")
            
            file_size = Path(audio_path).stat().st_size
            self.logger.info(f"Audio extracted: {audio_path} ({file_size / 1024 / 1024:.1f} MB)")
            
            return audio_path
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Audio extraction failed: {e.stderr}")
            raise
    
    def _transcribe_with_whisper(self, audio_path: str) -> str:
        """Transcribe audio using Whisper."""
        try:
            import whisper
            
            self.logger.info("Loading Whisper model...")
            
            # Load appropriate model based on config
            model_size = getattr(self.config, 'whisper_model_size', 'base')
            model = whisper.load_model(model_size)
            
            self.logger.info("Transcribing with Whisper...")
            
            # Transcribe
            result = model.transcribe(
                audio_path,
                language=None if self.config.language == 'auto' else self.config.language,
                verbose=False
            )
            
            # Extract text with timestamps
            transcript_parts = []
            for segment in result['segments']:
                start_time = segment['start']
                end_time = segment['end']
                text = segment['text'].strip()
                
                # Format: [MM:SS] Text
                timestamp = f"[{int(start_time//60):02d}:{int(start_time%60):02d}]"
                transcript_parts.append(f"{timestamp} {text}")
            
            transcript = '\n'.join(transcript_parts)
            
            self.logger.info(f"Whisper transcription completed. Detected language: {result.get('language', 'unknown')}")
            
            return transcript
            
        except Exception as e:
            self.logger.error(f"Whisper transcription failed: {e}")
            return self._transcribe_fallback(audio_path)
    
    def _transcribe_fallback(self, audio_path: str) -> str:
        """Fallback transcription method when Whisper is not available."""
        self.logger.warning("Using fallback transcription method")
        
        # Try using speech_recognition library if available
        try:
            import speech_recognition as sr
            
            recognizer = sr.Recognizer()
            
            with sr.AudioFile(audio_path) as source:
                audio_data = recognizer.record(source)
            
            # Try Google Speech Recognition (requires internet)
            try:
                transcript = recognizer.recognize_google(audio_data)
                self.logger.info("Transcription completed using Google Speech Recognition")
                return transcript
            except sr.UnknownValueError:
                self.logger.warning("Speech recognition could not understand audio")
            except sr.RequestError as e:
                self.logger.warning(f"Speech recognition service error: {e}")
            
        except ImportError:
            self.logger.warning("speech_recognition library not available")
        
        # Ultimate fallback - return placeholder
        self.logger.warning("No transcription method available. Using placeholder.")
        return "[Transcription not available - please install Whisper or speech_recognition]"
    
    def get_transcript_with_timestamps(self, video_path: str) -> Dict[str, Any]:
        """
        Get detailed transcript with precise timestamps.
        
        Returns:
            Dictionary with transcript data including word-level timestamps
        """
        if not self.whisper_available:
            self.logger.warning("Detailed timestamps require Whisper")
            return {
                'text': self.transcribe(video_path),
                'segments': [],
                'words': []
            }
        
        audio_path = self._extract_audio(video_path)
        
        try:
            import whisper
            
            model = whisper.load_model(getattr(self.config, 'whisper_model_size', 'base'))
            
            result = model.transcribe(
                audio_path,
                language=None if self.config.language == 'auto' else self.config.language,
                word_timestamps=True,
                verbose=False
            )
            
            return {
                'text': result['text'],
                'language': result['language'],
                'segments': result['segments'],
                'words': [word for segment in result['segments'] for word in segment.get('words', [])]
            }
            
        finally:
            if Path(audio_path).exists():
                Path(audio_path).unlink()
    
    def save_transcript(self, transcript: str, output_path: str):
        """Save transcript to file."""
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(transcript)
        
        self.logger.info(f"Transcript saved to: {output_path}")


if __name__ == "__main__":
    # Test the transcription engine
    from config import ClipperConfig
    
    config = ClipperConfig()
    transcriber = TranscriptionEngine(config)
    
    print(f"Transcription engine initialized")
    print(f"Whisper available: {transcriber.whisper_available}")
    print(f"FFmpeg path: {config.ffmpeg_path}")
    
    # Test audio extraction (would need actual video file)
    # transcript = transcriber.transcribe("test_video.mp4")
