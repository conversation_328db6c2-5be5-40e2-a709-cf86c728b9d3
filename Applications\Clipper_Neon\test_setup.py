#!/usr/bin/env python3
"""
Clipper_Neon Setup Test Script
=============================

Tests all components of the Clipper_Neon system to ensure proper setup.
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

def test_imports():
    """Test if all modules can be imported."""
    print("🧪 Testing module imports...")
    
    try:
        from src.config import ClipperConfig
        print("  ✅ Config module imported successfully")
    except Exception as e:
        print(f"  ❌ Config import failed: {e}")
        return False
    
    try:
        from src.video_downloader import VideoDownloader
        print("  ✅ Video downloader module imported successfully")
    except Exception as e:
        print(f"  ❌ Video downloader import failed: {e}")
        return False
    
    try:
        from src.transcription_engine import TranscriptionEngine
        print("  ✅ Transcription engine module imported successfully")
    except Exception as e:
        print(f"  ❌ Transcription engine import failed: {e}")
        return False
    
    try:
        from src.highlight_detector import HighlightDetector
        print("  ✅ Highlight detector module imported successfully")
    except Exception as e:
        print(f"  ❌ Highlight detector import failed: {e}")
        return False
    
    try:
        from src.video_clipper import VideoClipper
        print("  ✅ Video clipper module imported successfully")
    except Exception as e:
        print(f"  ❌ Video clipper import failed: {e}")
        return False
    
    try:
        from src.post_processor import PostProcessor
        print("  ✅ Post processor module imported successfully")
    except Exception as e:
        print(f"  ❌ Post processor import failed: {e}")
        return False
    
    try:
        from src.clipper_main import ClipperNeon
        print("  ✅ Main clipper module imported successfully")
    except Exception as e:
        print(f"  ❌ Main clipper import failed: {e}")
        return False
    
    return True


def test_configuration():
    """Test configuration setup."""
    print("\n⚙️  Testing configuration...")
    
    try:
        from src.config import ClipperConfig
        
        config = ClipperConfig()
        
        # Test paths
        if Path(config.ffmpeg_path).exists():
            print(f"  ✅ ffmpeg found at: {config.ffmpeg_path}")
        else:
            print(f"  ❌ ffmpeg not found at: {config.ffmpeg_path}")
            return False
        
        if Path(config.ytdlp_path).exists():
            print(f"  ✅ yt-dlp found at: {config.ytdlp_path}")
        else:
            print(f"  ❌ yt-dlp not found at: {config.ytdlp_path}")
            return False
        
        # Test validation
        if config.validate():
            print("  ✅ Configuration validation passed")
        else:
            print("  ❌ Configuration validation failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ Configuration test failed: {e}")
        return False


def test_ollama_connection():
    """Test connection to Ollama service."""
    print("\n🤖 Testing Ollama connection...")
    
    try:
        import requests
        from src.config import ClipperConfig
        
        config = ClipperConfig()
        
        # Test Ollama service
        response = requests.get(f"{config.ollama_host}/api/tags", timeout=5)
        
        if response.status_code == 200:
            models = response.json().get('models', [])
            model_names = [model['name'] for model in models]
            
            print(f"  ✅ Ollama service running at: {config.ollama_host}")
            print(f"  📦 Available models: {', '.join(model_names)}")
            
            if config.highlight_detection_model in model_names:
                print(f"  ✅ Target model '{config.highlight_detection_model}' is available")
                return True
            else:
                print(f"  ⚠️  Target model '{config.highlight_detection_model}' not found")
                print(f"      Available models: {model_names}")
                return False
        else:
            print(f"  ❌ Ollama service not responding (status: {response.status_code})")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"  ❌ Cannot connect to Ollama at {config.ollama_host}")
        print("      Make sure Ollama is running: ollama serve")
        return False
    except Exception as e:
        print(f"  ❌ Ollama connection test failed: {e}")
        return False


def test_optional_dependencies():
    """Test optional dependencies."""
    print("\n📦 Testing optional dependencies...")
    
    # Test Whisper
    try:
        import whisper
        print("  ✅ Whisper is available for transcription")
    except ImportError:
        print("  ⚠️  Whisper not available (install with: pip install openai-whisper)")
    
    # Test SpeechRecognition
    try:
        import speech_recognition
        print("  ✅ SpeechRecognition is available as fallback")
    except ImportError:
        print("  ⚠️  SpeechRecognition not available (install with: pip install SpeechRecognition)")
    
    # Test ffmpeg-python
    try:
        import ffmpeg
        print("  ✅ ffmpeg-python is available")
    except ImportError:
        print("  ⚠️  ffmpeg-python not available (install with: pip install ffmpeg-python)")


def test_directory_structure():
    """Test directory structure."""
    print("\n📁 Testing directory structure...")
    
    project_root = Path(__file__).parent
    
    required_dirs = [
        "src",
        "data_in", 
        "data_out",
        "configs",
        "logs",
        "temp"
    ]
    
    all_good = True
    
    for dir_name in required_dirs:
        dir_path = project_root / dir_name
        if dir_path.exists():
            print(f"  ✅ {dir_name}/ directory exists")
        else:
            print(f"  ❌ {dir_name}/ directory missing")
            all_good = False
    
    return all_good


def test_url_validation():
    """Test URL validation."""
    print("\n🔗 Testing URL validation...")
    
    try:
        from src.video_downloader import VideoDownloader
        from src.config import ClipperConfig
        
        config = ClipperConfig()
        downloader = VideoDownloader(config)
        
        test_urls = [
            ("https://www.youtube.com/watch?v=dQw4w9WgXcQ", True),
            ("https://youtu.be/dQw4w9WgXcQ", True),
            ("invalid_url", False),
            ("https://example.com", False)
        ]
        
        all_passed = True
        
        for url, expected in test_urls:
            result = downloader.validate_url(url)
            if result == expected:
                status = "✅" if expected else "✅"
                print(f"  {status} URL validation correct: {url}")
            else:
                print(f"  ❌ URL validation failed: {url} (expected {expected}, got {result})")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"  ❌ URL validation test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 Clipper_Neon Setup Test Suite")
    print("=" * 50)
    
    tests = [
        ("Module Imports", test_imports),
        ("Configuration", test_configuration),
        ("Ollama Connection", test_ollama_connection),
        ("Directory Structure", test_directory_structure),
        ("URL Validation", test_url_validation),
        ("Optional Dependencies", test_optional_dependencies),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Clipper_Neon is ready to use.")
        print("\nNext steps:")
        print("1. Install optional dependencies if needed:")
        print("   pip install openai-whisper")
        print("2. Test with a real YouTube video:")
        print("   python src/clipper_main.py 'https://youtube.com/watch?v=...'")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please fix the issues above.")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
