"""
Auto Uploader Module for Clipper_Neon
=====================================

Handles automated uploading to social media platforms using browser automation.
Zero API costs, complete control over posting.
"""

import os
import json
import time
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

try:
    from playwright.sync_api import sync_playwright, Browser, Page
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False


@dataclass
class UploadJob:
    """Data class for an upload job."""
    platform: str
    video_path: str
    title: str
    description: str
    tags: List[str]
    thumbnail_path: Optional[str] = None


class AutoUploader:
    """Handles automated uploading to social media platforms."""
    
    def __init__(self, config):
        """Initialize the auto uploader."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        if not PLAYWRIGHT_AVAILABLE:
            self.logger.warning("Playwright not available. Install with: pip install playwright")
            self.logger.warning("Then run: playwright install")
        
        # Load platform configurations
        self.platform_configs = self._load_platform_configs()
    
    def _load_platform_configs(self) -> Dict[str, Dict]:
        """Load platform-specific configurations."""
        return {
            'youtube_shorts': {
                'url': 'https://studio.youtube.com',
                'upload_selector': '[aria-label="Create"]',
                'video_selector': 'input[type="file"]',
                'title_selector': '[aria-label="Title"]',
                'description_selector': '[aria-label="Description"]',
                'shorts_selector': '[aria-label="Shorts"]',
                'publish_selector': '[aria-label="Publish"]'
            },
            'tiktok': {
                'url': 'https://www.tiktok.com/upload',
                'video_selector': 'input[type="file"]',
                'caption_selector': '[data-testid="caption-input"]',
                'post_selector': '[data-testid="post-button"]'
            },
            'instagram': {
                'url': 'https://www.instagram.com',
                'new_post_selector': '[aria-label="New post"]',
                'video_selector': 'input[type="file"]',
                'caption_selector': '[aria-label="Write a caption..."]',
                'share_selector': '[tabindex="0"]:has-text("Share")'
            }
        }
    
    def upload_batch(self, upload_jobs: List[UploadJob]) -> Dict[str, bool]:
        """
        Upload multiple videos to various platforms.
        
        Args:
            upload_jobs: List of UploadJob objects
            
        Returns:
            Dictionary mapping job descriptions to success status
        """
        if not PLAYWRIGHT_AVAILABLE:
            self.logger.error("Playwright not available for uploads")
            return {}
        
        results = {}
        
        with sync_playwright() as p:
            # Launch browser with persistent context for login sessions
            browser = p.chromium.launch(
                headless=False,  # Set to True for production
                args=['--no-sandbox', '--disable-dev-shm-usage']
            )
            
            try:
                for job in upload_jobs:
                    job_key = f"{job.platform}_{Path(job.video_path).stem}"
                    
                    try:
                        success = self._upload_single(browser, job)
                        results[job_key] = success
                        
                        if success:
                            self.logger.info(f"✅ Upload successful: {job_key}")
                        else:
                            self.logger.error(f"❌ Upload failed: {job_key}")
                        
                        # Wait between uploads to avoid rate limiting
                        time.sleep(5)
                        
                    except Exception as e:
                        self.logger.error(f"Upload error for {job_key}: {e}")
                        results[job_key] = False
            
            finally:
                browser.close()
        
        return results
    
    def _upload_single(self, browser: Browser, job: UploadJob) -> bool:
        """Upload a single video to a platform."""
        platform_config = self.platform_configs.get(job.platform)
        
        if not platform_config:
            self.logger.error(f"Unsupported platform: {job.platform}")
            return False
        
        # Create new page for this upload
        page = browser.new_page()
        
        try:
            # Navigate to platform
            page.goto(platform_config['url'])
            
            # Platform-specific upload logic
            if job.platform == 'youtube_shorts':
                return self._upload_youtube_shorts(page, job, platform_config)
            elif job.platform == 'tiktok':
                return self._upload_tiktok(page, job, platform_config)
            elif job.platform == 'instagram':
                return self._upload_instagram(page, job, platform_config)
            else:
                self.logger.error(f"Upload method not implemented for: {job.platform}")
                return False
        
        except Exception as e:
            self.logger.error(f"Upload failed for {job.platform}: {e}")
            return False
        
        finally:
            page.close()
    
    def _upload_youtube_shorts(self, page: Page, job: UploadJob, config: Dict) -> bool:
        """Upload video to YouTube Shorts."""
        try:
            # Wait for page load and click create button
            page.wait_for_selector(config['upload_selector'], timeout=30000)
            page.click(config['upload_selector'])
            
            # Upload video file
            page.wait_for_selector(config['video_selector'], timeout=10000)
            page.set_input_files(config['video_selector'], job.video_path)
            
            # Fill title
            page.wait_for_selector(config['title_selector'], timeout=15000)
            page.fill(config['title_selector'], job.title)
            
            # Fill description
            if config.get('description_selector'):
                page.fill(config['description_selector'], job.description)
            
            # Mark as Shorts if selector exists
            if config.get('shorts_selector'):
                try:
                    page.click(config['shorts_selector'])
                except:
                    pass  # Shorts detection might be automatic
            
            # Publish
            page.wait_for_selector(config['publish_selector'], timeout=10000)
            page.click(config['publish_selector'])
            
            # Wait for upload completion
            page.wait_for_timeout(5000)
            
            return True
            
        except Exception as e:
            self.logger.error(f"YouTube Shorts upload error: {e}")
            return False
    
    def _upload_tiktok(self, page: Page, job: UploadJob, config: Dict) -> bool:
        """Upload video to TikTok."""
        try:
            # Upload video file
            page.wait_for_selector(config['video_selector'], timeout=30000)
            page.set_input_files(config['video_selector'], job.video_path)
            
            # Wait for video processing
            page.wait_for_timeout(10000)
            
            # Fill caption
            page.wait_for_selector(config['caption_selector'], timeout=15000)
            caption = f"{job.title}\n\n{job.description}"
            page.fill(config['caption_selector'], caption)
            
            # Post
            page.wait_for_selector(config['post_selector'], timeout=10000)
            page.click(config['post_selector'])
            
            # Wait for upload completion
            page.wait_for_timeout(5000)
            
            return True
            
        except Exception as e:
            self.logger.error(f"TikTok upload error: {e}")
            return False
    
    def _upload_instagram(self, page: Page, job: UploadJob, config: Dict) -> bool:
        """Upload video to Instagram."""
        try:
            # Click new post
            page.wait_for_selector(config['new_post_selector'], timeout=30000)
            page.click(config['new_post_selector'])
            
            # Upload video file
            page.wait_for_selector(config['video_selector'], timeout=10000)
            page.set_input_files(config['video_selector'], job.video_path)
            
            # Navigate through Instagram's upload flow
            # (This is simplified - Instagram has multiple steps)
            page.wait_for_timeout(5000)
            
            # Fill caption
            page.wait_for_selector(config['caption_selector'], timeout=15000)
            caption = f"{job.title}\n\n{job.description}"
            page.fill(config['caption_selector'], caption)
            
            # Share
            page.wait_for_selector(config['share_selector'], timeout=10000)
            page.click(config['share_selector'])
            
            # Wait for upload completion
            page.wait_for_timeout(5000)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Instagram upload error: {e}")
            return False
    
    def create_upload_jobs_from_clips(
        self, 
        clip_paths: List[str], 
        highlights: List, 
        metadata: Dict[str, Any],
        platforms: List[str] = None
    ) -> List[UploadJob]:
        """
        Create upload jobs from processed clips.
        
        Args:
            clip_paths: List of clip file paths
            highlights: List of highlight objects
            metadata: Video metadata
            platforms: List of platforms to upload to
            
        Returns:
            List of UploadJob objects
        """
        if platforms is None:
            platforms = ['youtube_shorts', 'tiktok']  # Default platforms
        
        upload_jobs = []
        
        for i, (clip_path, highlight) in enumerate(zip(clip_paths, highlights)):
            for platform in platforms:
                # Create platform-specific title and description
                title, description = self._format_content_for_platform(
                    highlight, metadata, platform
                )
                
                job = UploadJob(
                    platform=platform,
                    video_path=clip_path,
                    title=title,
                    description=description,
                    tags=highlight.keywords
                )
                
                upload_jobs.append(job)
        
        return upload_jobs
    
    def _format_content_for_platform(
        self, 
        highlight, 
        metadata: Dict[str, Any], 
        platform: str
    ) -> tuple:
        """Format title and description for specific platform."""
        base_title = highlight.title
        base_description = highlight.description
        
        if platform == 'youtube_shorts':
            title = f"{base_title} #Shorts"
            description = f"{base_description}\n\nFrom: {metadata.get('title', '')}"
            
        elif platform == 'tiktok':
            title = base_title
            description = f"{base_description} #fyp #viral"
            
        elif platform == 'instagram':
            title = base_title
            description = f"{base_description} #reels #viral"
            
        else:
            title = base_title
            description = base_description
        
        return title, description


if __name__ == "__main__":
    # Test the auto uploader
    from config import ClipperConfig
    
    config = ClipperConfig()
    uploader = AutoUploader(config)
    
    print(f"Auto uploader initialized")
    print(f"Playwright available: {PLAYWRIGHT_AVAILABLE}")
    print(f"Supported platforms: {list(uploader.platform_configs.keys())}")
