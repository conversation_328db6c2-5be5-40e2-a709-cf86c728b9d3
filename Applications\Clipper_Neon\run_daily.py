#!/usr/bin/env python3
"""
Daily Automation Runner
======================

Simple script to run daily automation with predefined settings.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from daily_automation import DailyAutomation

def main():
    # URLs to process daily (modify as needed)
    daily_urls = [
        # Add your daily YouTube URLs here
        # "https://youtube.com/watch?v=VIDEO_ID"
    ]
    
    # Read URLs from file if it exists
    urls_file = Path(__file__).parent / "data_in" / "daily_urls.txt"
    if urls_file.exists():
        with open(urls_file, 'r') as f:
            file_urls = [line.strip() for line in f if line.strip() and not line.startswith('#')]
        daily_urls.extend(file_urls)
    
    if not daily_urls:
        print("No URLs configured for daily processing")
        print(f"Add URLs to: {urls_file}")
        return
    
    # Run automation
    automation = DailyAutomation()
    automation.run_daily_workflow(daily_urls, platforms=['youtube_shorts', 'tiktok'])

if __name__ == "__main__":
    main()
