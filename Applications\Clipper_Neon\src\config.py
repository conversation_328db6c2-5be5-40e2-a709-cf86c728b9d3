"""
Configuration management for Clipper_Neon
=========================================

Handles all configuration settings and paths for the YouTube clipping system.
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class ClipperConfig:
    """Configuration class for Clipper_Neon system."""
    
    # Paths
    project_root: str = ""
    utilities_path: str = ""
    output_dir: str = ""
    temp_dir: str = ""
    
    # AI/LLM Settings
    ollama_host: str = "http://localhost:11434"
    default_model: str = "deepseek-r1"
    highlight_detection_model: str = "deepseek-r1"
    
    # Video Processing
    max_clips_per_video: int = 5
    min_clip_duration: float = 10.0  # seconds
    max_clip_duration: float = 60.0  # seconds
    video_quality: str = "720p"
    audio_quality: str = "192k"
    
    # Transcription
    transcription_model: str = "whisper"
    language: str = "auto"
    
    # Highlight Detection
    highlight_confidence_threshold: float = 0.7
    viral_keywords: list = None
    
    # Output Settings
    clip_format: str = "mp4"
    include_captions: bool = True
    watermark_enabled: bool = False
    
    # Logging
    log_level: str = "INFO"
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize configuration with defaults and load from file if provided."""
        # Set default paths
        self.project_root = str(Path(__file__).parent.parent)
        self.utilities_path = str(Path(self.project_root).parent.parent / "Core" / "Utilities")
        self.output_dir = str(Path(self.project_root) / "data_out")
        self.temp_dir = str(Path(self.project_root) / "temp")
        
        # Default viral keywords for highlight detection
        self.viral_keywords = [
            "amazing", "incredible", "unbelievable", "shocking", "viral",
            "trending", "must see", "epic", "insane", "crazy", "wow",
            "breakthrough", "exclusive", "secret", "revealed", "exposed"
        ]
        
        # Load from file if provided
        if config_path and Path(config_path).exists():
            self.load_from_file(config_path)
        
        # Create necessary directories
        self._create_directories()
    
    def load_from_file(self, config_path: str):
        """Load configuration from JSON file."""
        try:
            with open(config_path, 'r') as f:
                config_data = json.load(f)
            
            # Update attributes with loaded data
            for key, value in config_data.items():
                if hasattr(self, key):
                    setattr(self, key, value)
                    
        except Exception as e:
            print(f"Warning: Could not load config from {config_path}: {e}")
    
    def save_to_file(self, config_path: str):
        """Save current configuration to JSON file."""
        config_data = asdict(self)
        
        with open(config_path, 'w') as f:
            json.dump(config_data, f, indent=2)
    
    def _create_directories(self):
        """Create necessary directories if they don't exist."""
        directories = [
            self.output_dir,
            self.temp_dir,
            str(Path(self.project_root) / "logs"),
            str(Path(self.project_root) / "data_in"),
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    @property
    def ffmpeg_path(self) -> str:
        """Get path to ffmpeg executable."""
        return str(Path(self.utilities_path) / "ffmpeg.exe")
    
    @property
    def ytdlp_path(self) -> str:
        """Get path to yt-dlp executable."""
        return str(Path(self.utilities_path) / "yt-dlp.exe")
    
    def get_highlight_prompt(self) -> str:
        """Get the prompt template for highlight detection."""
        return """
You are an expert content curator specializing in identifying viral-worthy moments from video transcripts.

Analyze the following transcript and identify the TOP {max_clips} most engaging, shareable, and viral-worthy moments.

For each highlight, provide:
1. Start timestamp (in seconds)
2. End timestamp (in seconds) 
3. Compelling title (hook-worthy, under 60 characters)
4. Brief description (why this moment is engaging)
5. Confidence score (0.0-1.0)
6. Key viral elements present

Focus on moments that contain:
- Surprising revelations or plot twists
- Emotional peaks (excitement, shock, humor)
- Actionable insights or valuable information
- Controversial or debate-worthy statements
- Visual or auditory highlights
- Quotable moments
- Educational breakthroughs

Avoid:
- Transitions or filler content
- Repetitive information
- Low-energy segments
- Technical difficulties or interruptions

Transcript:
{transcript}

Video Metadata:
Title: {title}
Duration: {duration} seconds
Description: {description}

Respond in JSON format:
{{
  "highlights": [
    {{
      "start_time": 45.2,
      "end_time": 78.5,
      "title": "Mind-Blowing Revelation About...",
      "description": "Why this moment will hook viewers",
      "confidence": 0.95,
      "keywords": ["shocking", "revelation", "viral"]
    }}
  ]
}}
"""
    
    def validate(self) -> bool:
        """Validate configuration settings."""
        errors = []
        
        # Check required paths exist
        if not Path(self.ffmpeg_path).exists():
            errors.append(f"ffmpeg not found at: {self.ffmpeg_path}")
        
        if not Path(self.ytdlp_path).exists():
            errors.append(f"yt-dlp not found at: {self.ytdlp_path}")
        
        # Check numeric ranges
        if self.max_clips_per_video < 1 or self.max_clips_per_video > 20:
            errors.append("max_clips_per_video must be between 1 and 20")
        
        if self.min_clip_duration >= self.max_clip_duration:
            errors.append("min_clip_duration must be less than max_clip_duration")
        
        if self.highlight_confidence_threshold < 0.0 or self.highlight_confidence_threshold > 1.0:
            errors.append("highlight_confidence_threshold must be between 0.0 and 1.0")
        
        if errors:
            print("Configuration validation errors:")
            for error in errors:
                print(f"  - {error}")
            return False
        
        return True


def create_default_config_file(output_path: str):
    """Create a default configuration file."""
    config = ClipperConfig()
    config.save_to_file(output_path)
    print(f"Default configuration saved to: {output_path}")


if __name__ == "__main__":
    # Create default config file for reference
    config_path = Path(__file__).parent.parent / "configs" / "default_config.json"
    create_default_config_file(str(config_path))
