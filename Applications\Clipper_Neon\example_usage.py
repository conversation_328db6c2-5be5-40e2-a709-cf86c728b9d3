#!/usr/bin/env python3
"""
Clipper_Neon Example Usage
=========================

Demonstrates how to use Clipper_Neon programmatically.
"""

import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.clipper_main import ClipperNeon
from src.config import ClipperConfig


def example_single_video():
    """Example: Process a single YouTube video."""
    print("🎬 Example: Processing Single Video")
    print("=" * 40)
    
    # Initialize Clipper_Neon
    config = ClipperConfig()
    clipper = ClipperNeon()
    
    # Example YouTube URL (replace with actual URL)
    youtube_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
    
    try:
        print(f"Processing: {youtube_url}")
        
        # Process the video
        clips = clipper.process_video(youtube_url)
        
        print(f"\n✅ Success! Generated {len(clips)} clips:")
        for i, clip_path in enumerate(clips, 1):
            print(f"  {i}. {clip_path}")
        
        return clips
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return []


def example_batch_processing():
    """Example: Process multiple videos from a list."""
    print("\n🎬 Example: Batch Processing")
    print("=" * 40)
    
    # Create example URLs file
    urls_file = "example_urls.txt"
    example_urls = [
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
        "https://www.youtube.com/watch?v=9bZkp7q19f0",
        # Add more URLs as needed
    ]
    
    # Write URLs to file
    with open(urls_file, 'w') as f:
        for url in example_urls:
            f.write(f"{url}\n")
    
    print(f"Created {urls_file} with {len(example_urls)} URLs")
    
    # Initialize Clipper_Neon
    clipper = ClipperNeon()
    
    try:
        # Process batch
        results = clipper.process_batch(urls_file)
        
        print(f"\n✅ Batch processing complete!")
        print(f"Processed {len(results)} videos:")
        
        for url, clips in results.items():
            print(f"\n📹 {url}")
            print(f"   Generated {len(clips)} clips")
            for clip in clips:
                print(f"   - {Path(clip).name}")
        
        return results
        
    except Exception as e:
        print(f"❌ Batch processing error: {e}")
        return {}
    
    finally:
        # Clean up
        if Path(urls_file).exists():
            Path(urls_file).unlink()


def example_custom_configuration():
    """Example: Using custom configuration."""
    print("\n⚙️ Example: Custom Configuration")
    print("=" * 40)
    
    # Create custom config
    config = ClipperConfig()
    
    # Customize settings
    config.max_clips_per_video = 3
    config.min_clip_duration = 15.0
    config.max_clip_duration = 45.0
    config.highlight_confidence_threshold = 0.8
    config.video_quality = "480p"
    
    print("Custom configuration:")
    print(f"  Max clips per video: {config.max_clips_per_video}")
    print(f"  Clip duration: {config.min_clip_duration}s - {config.max_clip_duration}s")
    print(f"  Confidence threshold: {config.highlight_confidence_threshold}")
    print(f"  Video quality: {config.video_quality}")
    
    # Save custom config
    config_file = "custom_config.json"
    config.save_to_file(config_file)
    print(f"  Saved to: {config_file}")
    
    # Use custom config
    clipper = ClipperNeon(config_file)
    print("✅ Clipper initialized with custom configuration")
    
    return config


def example_component_testing():
    """Example: Test individual components."""
    print("\n🧪 Example: Component Testing")
    print("=" * 40)
    
    from src.config import ClipperConfig
    from src.video_downloader import VideoDownloader
    from src.highlight_detector import HighlightDetector
    
    config = ClipperConfig()
    
    # Test URL validation
    print("Testing URL validation:")
    downloader = VideoDownloader(config)
    
    test_urls = [
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
        "https://youtu.be/dQw4w9WgXcQ", 
        "invalid_url"
    ]
    
    for url in test_urls:
        is_valid = downloader.validate_url(url)
        status = "✅" if is_valid else "❌"
        print(f"  {status} {url}")
    
    # Test AI connection
    print("\nTesting AI connection:")
    try:
        detector = HighlightDetector(config)
        print("  ✅ DeepSeek-R1 connection successful")
    except Exception as e:
        print(f"  ❌ AI connection failed: {e}")
    
    # Test configuration validation
    print("\nTesting configuration:")
    if config.validate():
        print("  ✅ Configuration is valid")
    else:
        print("  ❌ Configuration has issues")


def main():
    """Run all examples."""
    print("🚀 Clipper_Neon Examples")
    print("=" * 50)
    
    try:
        # Test components first
        example_component_testing()
        
        # Custom configuration example
        example_custom_configuration()
        
        # Note: Uncomment these to test with real videos
        # example_single_video()
        # example_batch_processing()
        
        print("\n🎉 Examples completed successfully!")
        print("\nTo test with real videos:")
        print("1. Uncomment the video processing examples above")
        print("2. Replace example URLs with real YouTube URLs")
        print("3. Ensure Ollama is running: ollama serve")
        print("4. Run: python example_usage.py")
        
    except KeyboardInterrupt:
        print("\n⏹️ Examples interrupted by user")
    except Exception as e:
        print(f"\n❌ Example failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
