"""
Auto Processor Module for Clipper_Neon
======================================

Handles automated daily processing and uploading workflow.
Monitors for new URLs, processes videos, and uploads clips automatically.
"""

import os
import json
import time
import logging
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime, timedelta
from dataclasses import dataclass

try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
    WATCHDOG_AVAILABLE = True
except ImportError:
    WATCHDOG_AVAILABLE = False

from clipper_main import Clipper<PERSON>eon
from auto_uploader import AutoUploader, UploadJob


@dataclass
class ProcessingJob:
    """Data class for a processing job."""
    url: str
    priority: int = 1
    platforms: List[str] = None
    max_clips: int = 5
    auto_upload: bool = True


class URLFileHandler(FileSystemEventHandler):
    """Handles new URL files for automatic processing."""
    
    def __init__(self, processor):
        self.processor = processor
        self.logger = logging.getLogger(__name__)
    
    def on_created(self, event):
        """Handle new file creation."""
        if event.is_directory:
            return
        
        file_path = Path(event.src_path)
        
        # Process .txt files containing URLs
        if file_path.suffix.lower() == '.txt' and 'urls' in file_path.name.lower():
            self.logger.info(f"New URL file detected: {file_path}")
            time.sleep(1)  # Wait for file write completion
            self.processor.process_url_file(str(file_path))


class AutoProcessor:
    """Handles automated video processing and uploading."""
    
    def __init__(self, config):
        """Initialize the auto processor."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.clipper = ClipperNeon(config)
        self.uploader = AutoUploader(config)
        
        # Processing queue
        self.processing_queue = []
        self.processing_history = []
        
        # File watcher
        self.observer = None
        if WATCHDOG_AVAILABLE:
            self.setup_file_watcher()
        else:
            self.logger.warning("Watchdog not available. Install with: pip install watchdog")
    
    def setup_file_watcher(self):
        """Setup file system watcher for automatic processing."""
        watch_dir = Path(self.config.project_root) / "data_in"
        watch_dir.mkdir(exist_ok=True)
        
        self.observer = Observer()
        handler = URLFileHandler(self)
        self.observer.schedule(handler, str(watch_dir), recursive=False)
        
        self.logger.info(f"File watcher setup for: {watch_dir}")
    
    def start_monitoring(self):
        """Start the file monitoring service."""
        if not self.observer:
            self.logger.error("File watcher not available")
            return
        
        self.observer.start()
        self.logger.info("🔍 Auto processor monitoring started")
        
        try:
            while True:
                # Process queued jobs
                self.process_queue()
                
                # Daily cleanup
                self.daily_cleanup()
                
                # Wait before next cycle
                time.sleep(60)  # Check every minute
                
        except KeyboardInterrupt:
            self.logger.info("Auto processor stopped by user")
        finally:
            self.stop_monitoring()
    
    def stop_monitoring(self):
        """Stop the file monitoring service."""
        if self.observer:
            self.observer.stop()
            self.observer.join()
            self.logger.info("Auto processor monitoring stopped")
    
    def process_url_file(self, file_path: str):
        """Process a file containing YouTube URLs."""
        try:
            with open(file_path, 'r') as f:
                urls = [line.strip() for line in f if line.strip()]
            
            self.logger.info(f"Found {len(urls)} URLs in {file_path}")
            
            # Add to processing queue
            for url in urls:
                if self.is_valid_youtube_url(url):
                    job = ProcessingJob(
                        url=url,
                        platforms=['youtube_shorts', 'tiktok'],
                        max_clips=3,
                        auto_upload=True
                    )
                    self.add_to_queue(job)
                else:
                    self.logger.warning(f"Invalid YouTube URL: {url}")
            
            # Move processed file
            processed_dir = Path(file_path).parent / "processed"
            processed_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            new_name = f"processed_{timestamp}_{Path(file_path).name}"
            new_path = processed_dir / new_name
            
            Path(file_path).rename(new_path)
            self.logger.info(f"Moved processed file to: {new_path}")
            
        except Exception as e:
            self.logger.error(f"Error processing URL file {file_path}: {e}")
    
    def add_to_queue(self, job: ProcessingJob):
        """Add a processing job to the queue."""
        # Check if URL already processed recently
        if self.is_recently_processed(job.url):
            self.logger.info(f"URL recently processed, skipping: {job.url}")
            return
        
        self.processing_queue.append(job)
        self.logger.info(f"Added to queue: {job.url} (queue size: {len(self.processing_queue)})")
    
    def process_queue(self):
        """Process jobs in the queue."""
        if not self.processing_queue:
            return
        
        # Sort by priority
        self.processing_queue.sort(key=lambda x: x.priority, reverse=True)
        
        # Process one job at a time
        job = self.processing_queue.pop(0)
        
        try:
            self.logger.info(f"🎬 Processing: {job.url}")
            
            # Process video
            clips = self.clipper.process_video(job.url)
            
            if clips and job.auto_upload:
                # Create upload jobs
                upload_jobs = self.create_upload_jobs(clips, job)
                
                # Upload to platforms
                if upload_jobs:
                    self.logger.info(f"📤 Uploading {len(upload_jobs)} clips to platforms")
                    results = self.uploader.upload_batch(upload_jobs)
                    
                    # Log results
                    successful = sum(1 for success in results.values() if success)
                    self.logger.info(f"Upload results: {successful}/{len(results)} successful")
            
            # Record in history
            self.processing_history.append({
                'url': job.url,
                'processed_at': datetime.now().isoformat(),
                'clips_generated': len(clips),
                'success': True
            })
            
            self.logger.info(f"✅ Completed processing: {job.url}")
            
        except Exception as e:
            self.logger.error(f"❌ Processing failed for {job.url}: {e}")
            
            # Record failure in history
            self.processing_history.append({
                'url': job.url,
                'processed_at': datetime.now().isoformat(),
                'clips_generated': 0,
                'success': False,
                'error': str(e)
            })
    
    def create_upload_jobs(self, clips: List[str], job: ProcessingJob) -> List[UploadJob]:
        """Create upload jobs from processed clips."""
        # This is a simplified version - in practice, you'd get highlights and metadata
        upload_jobs = []
        
        for i, clip_path in enumerate(clips):
            for platform in job.platforms or ['youtube_shorts']:
                upload_job = UploadJob(
                    platform=platform,
                    video_path=clip_path,
                    title=f"Viral Moment {i+1}",
                    description="Auto-generated viral clip",
                    tags=['viral', 'trending', 'shorts']
                )
                upload_jobs.append(upload_job)
        
        return upload_jobs
    
    def is_valid_youtube_url(self, url: str) -> bool:
        """Check if URL is a valid YouTube URL."""
        return 'youtube.com/watch' in url or 'youtu.be/' in url
    
    def is_recently_processed(self, url: str, hours: int = 24) -> bool:
        """Check if URL was processed recently."""
        cutoff = datetime.now() - timedelta(hours=hours)
        
        for record in self.processing_history:
            if record['url'] == url:
                processed_time = datetime.fromisoformat(record['processed_at'])
                if processed_time > cutoff:
                    return True
        
        return False
    
    def daily_cleanup(self):
        """Perform daily cleanup tasks."""
        now = datetime.now()
        
        # Run cleanup once per day
        if hasattr(self, '_last_cleanup'):
            if (now - self._last_cleanup).total_seconds() < 86400:  # 24 hours
                return
        
        self.logger.info("🧹 Running daily cleanup")
        
        # Clean old temporary files
        temp_dir = Path(self.config.temp_dir)
        cutoff = now - timedelta(days=1)
        
        for file_path in temp_dir.glob("*"):
            if file_path.is_file():
                file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                if file_time < cutoff:
                    try:
                        file_path.unlink()
                        self.logger.debug(f"Cleaned up old file: {file_path}")
                    except Exception as e:
                        self.logger.warning(f"Could not clean up {file_path}: {e}")
        
        # Trim processing history (keep last 1000 records)
        if len(self.processing_history) > 1000:
            self.processing_history = self.processing_history[-1000:]
        
        self._last_cleanup = now
        self.logger.info("Daily cleanup completed")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current processor status."""
        return {
            'queue_size': len(self.processing_queue),
            'history_size': len(self.processing_history),
            'monitoring': self.observer.is_alive() if self.observer else False,
            'last_processed': self.processing_history[-1] if self.processing_history else None
        }


def main():
    """Main entry point for auto processor."""
    from config import ClipperConfig
    
    config = ClipperConfig()
    processor = AutoProcessor(config)
    
    print("🚀 Clipper_Neon Auto Processor")
    print("=" * 40)
    print(f"Monitoring: {config.project_root}/data_in/")
    print("Drop URL files (*.txt) to auto-process videos")
    print("Press Ctrl+C to stop")
    print()
    
    try:
        processor.start_monitoring()
    except KeyboardInterrupt:
        print("\nStopping auto processor...")


if __name__ == "__main__":
    main()
